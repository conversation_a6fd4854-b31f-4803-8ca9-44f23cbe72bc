#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Morphik Python SDK 完整教程 - 第一部分：基础功能
==============================================

本教程将循序渐进地介绍 Morphik Python SDK 的主要功能，包含详细的中文注释。
教程分为多个部分，从基础功能开始，逐步深入到高级特性。

第一部分内容：
1. 连接与初始化
2. 文档摄取（文本和文件）
3. 基础检索与查询
4. 文档管理（列表、获取、更新、删除）

作者：AI助手
日期：2024年
"""

import os
import time
from typing import Dict, List, Any, Optional, Type
from pydantic import BaseModel

# 导入 Morphik 相关模块
from morphik import Morphik
from morphik.rules import MetadataExtractionRule, NaturalLanguageRule

# =====================================================================
# 1. 连接与初始化
# =====================================================================

def initialize_morphik():
    """
    初始化 Morphik 客户端
    
    Morphik 支持两种连接模式：
    1. 本地模式：连接到本地运行的 Morphik 服务器
    2. 远程模式：连接到远程 Morphik 服务器（需要认证）
    """
    print("\n===== 1. 连接与初始化 =====")
    
    # 方式一：连接到本地服务器（默认 localhost:8000）
    # 适用于开发环境或本地测试
    db_local = Morphik(is_local=True)
    print("已连接到本地 Morphik 服务器")
    
    # 方式二：连接到远程服务器（需要认证）
    # 适用于生产环境
    # 注意：这里使用环境变量存储认证信息，实际使用时请替换为您的认证信息
    morphik_uri = os.environ.get("MORPHIK_URI", "morphik://owner_id:<EMAIL>")
    
    # 使用 try-except 处理连接错误
    try:
        db_remote = Morphik(uri=morphik_uri, timeout=60)
        print("已连接到远程 Morphik 服务器")
    except Exception as e:
        print(f"连接远程服务器失败: {e}")
        # 如果远程连接失败，回退到本地连接
        db_remote = db_local
    
    # 方式三：使用上下文管理器（推荐方式）
    # 这种方式会自动管理资源，确保连接正确关闭
    print("使用上下文管理器连接...")
    with Morphik(is_local=True) as db_context:
        print("在上下文中连接成功")
        # 在上下文中执行操作...
    print("上下文已关闭")
    
    # 返回本地连接的客户端用于后续示例
    return db_local

# =====================================================================
# 2. 文档摄取（文本和文件）
# =====================================================================

def ingest_documents(db: Morphik):
    """
    演示如何摄取文本和文件到 Morphik
    
    文档摄取是使用 Morphik 的第一步，支持多种格式：
    - 纯文本
    - PDF、Word、Excel 等文件
    - 图像文件
    """
    print("\n===== 2. 文档摄取 =====")
    
    # ----- 2.1 基础文本摄取 -----
    print("\n--- 2.1 基础文本摄取 ---")
    
    # 简单文本摄取（最基本用法）
    simple_doc = db.ingest_text(
        content="这是一个简单的文本文档，用于演示 Morphik 的基本功能。",
        filename="simple_doc.txt"  # 可选：指定文件名
    )
    print(f"已摄取简单文本文档，ID: {simple_doc.external_id}")
    
    # 带元数据的文本摄取
    # 元数据可以帮助组织和过滤文档，非常有用
    metadata_doc = db.ingest_text(
        content="这是一个带有元数据的文档。元数据可以帮助我们更好地组织和检索文档。",
        filename="metadata_doc.txt",
        metadata={
            "类别": "教程",
            "作者": "AI助手",
            "日期": "2024-05-01",
            "标签": ["morphik", "教程", "元数据"]
        }
    )
    print(f"已摄取带元数据的文档，ID: {metadata_doc.external_id}")
    
    # ----- 2.2 使用规则的文本摄取 -----
    print("\n--- 2.2 使用规则的文本摄取 ---")
    
    # 定义元数据提取规则的 Pydantic 模型
    class ArticleInfo(BaseModel):
        """文章元数据模型"""
        标题: str
        作者: str
        发布日期: str
        关键词: List[str]
    
    # 准备一篇示例文章
    article_content = """
    # 人工智能在医疗领域的应用
    
    作者: 张明 | 发布日期: 2024-04-15
    
    ## 摘要
    
    本文探讨了人工智能技术在医疗领域的多种应用，包括疾病诊断、药物研发和医疗影像分析等。
    人工智能正在改变医疗行业的多个方面，提高诊断准确性，加速药物研发，并优化医疗资源分配。
    
    ## 关键应用
    
    在疾病诊断方面，机器学习算法可以分析患者数据，识别疾病模式，提供早期预警。
    在药物研发领域，AI可以预测分子结构，加速筛选过程，大幅缩短研发周期。
    在医疗影像分析中，深度学习模型能够检测X光片、CT和MRI扫描中的异常，辅助放射科医生工作。
    
    ## 挑战与前景
    
    尽管AI在医疗领域展现出巨大潜力，但仍面临数据隐私、算法透明度和监管等挑战。
    未来，随着技术进步和监管框架完善，AI将在医疗领域发挥更大作用，推动精准医疗发展。
    
    关键词: 人工智能, 医疗诊断, 药物研发, 医疗影像, 精准医疗
    """
    
    # 使用元数据提取规则和自然语言规则摄取文档
    rules_doc = db.ingest_text(
        content=article_content,
        filename="ai_in_healthcare.md",
        metadata={"领域": "医疗", "类型": "研究文章"},
        rules=[
            # 规则1: 使用 Pydantic 模型自动提取元数据
            MetadataExtractionRule(schema=ArticleInfo),
            
            # 规则2: 使用自然语言指令转换内容
            NaturalLanguageRule(
                prompt="将文章内容转换为简洁的要点列表，保留关键信息",
                # 可选: 指定规则应用的阶段
                # stage="post_parsing"  # 可选值: "post_parsing" 或 "post_chunking"
            )
        ]
    )
    print(f"已摄取使用规则处理的文档，ID: {rules_doc.external_id}")
    
    # 查看提取的元数据
    doc_info = db.get_document(rules_doc.external_id)
    print("自动提取的元数据:")
    for key, value in doc_info.metadata.items():
        if key not in ["领域", "类型"]:  # 只显示自动提取的元数据
            print(f"  - {key}: {value}")
    
    # ----- 2.3 文件摄取 -----
    print("\n--- 2.3 文件摄取 ---")
    print("注意: 此示例需要实际文件才能运行")
    print("文件摄取支持多种格式，包括 PDF、Word、Excel、图像等")
    
    # 文件摄取示例代码（注释掉，因为需要实际文件）
    """
    # 摄取 PDF 文件
    pdf_doc = db.ingest_file(
        file="path/to/document.pdf",
        metadata={"类别": "报告", "部门": "研发"}
    )
    print(f"已摄取 PDF 文件，ID: {pdf_doc.external_id}")
    
    # 摄取图像文件（使用 ColPali 多模态处理）
    image_doc = db.ingest_file(
        file="path/to/image.jpg",
        metadata={"类别": "图表", "项目": "销售分析"},
        use_colpali=True  # 启用多模态处理，可以理解图像内容
    )
    print(f"已摄取图像文件，ID: {image_doc.external_id}")
    """
    
    # 返回摄取的文档 ID 列表，用于后续示例
    return [simple_doc.external_id, metadata_doc.external_id, rules_doc.external_id]

# =====================================================================
# 3. 基础检索与查询
# =====================================================================

def retrieve_and_query(db: Morphik, doc_ids: List[str]):
    """
    演示基础检索和查询功能
    
    Morphik 提供两种主要的检索方式：
    1. retrieve_chunks: 检索相关文档块，返回原始内容
    2. query: 基于检索到的文档块生成回答（RAG）
    """
    print("\n===== 3. 基础检索与查询 =====")
    
    # 等待一下，确保文档已被索引
    print("等待文档索引完成...")
    time.sleep(3)
    
    # ----- 3.1 检索文档块 -----
    print("\n--- 3.1 检索文档块 ---")
    
    # 基本检索
    chunks = db.retrieve_chunks(
        query="人工智能在医疗领域的应用",
        k=3  # 返回前 3 个最相关的块
    )
    
    print(f"检索到 {len(chunks)} 个相关文档块")
    for i, chunk in enumerate(chunks):
        print(f"\n文档块 {i+1}:")
        print(f"  相关度分数: {chunk.score:.4f}")
        print(f"  文档 ID: {chunk.document_id}")
        print(f"  块编号: {chunk.chunk_number}")
        # 只显示内容的前 100 个字符
        print(f"  内容预览: {chunk.content[:100]}...")
    
    # 使用元数据过滤的检索
    filtered_chunks = db.retrieve_chunks(
        query="医疗应用",
        k=2,
        filters={"领域": "医疗"}  # 只检索医疗领域的文档
    )
    
    print(f"\n使用元数据过滤检索到 {len(filtered_chunks)} 个文档块")
    for i, chunk in enumerate(filtered_chunks):
        print(f"  文档块 {i+1} - 相关度: {chunk.score:.4f}")
    
    # ----- 3.2 查询（RAG）-----
    print("\n--- 3.2 查询（RAG）---")
    
    # 基本查询
    response = db.query(
        query="总结人工智能在医疗领域的主要应用",
        k=3,  # 使用前 3 个最相关的块
        temperature=0.7  # 控制生成的随机性
    )
    
    print("查询结果:")
    print(response.completion)
    
    print("\n使用的文档块来源:")
    for i, source in enumerate(response.sources):
        print(f"  来源 {i+1}: 文档 {source.document_id}, 块 {source.chunk_number}, 相关度: {source.score:.4f}")
    
    # 使用元数据过滤的查询
    filtered_response = db.query(
        query="人工智能在医疗诊断方面有哪些应用？",
        filters={"领域": "医疗"},
        temperature=0.5  # 降低温度以获得更确定的回答
    )
    
    print("\n使用元数据过滤的查询结果:")
    print(filtered_response.completion)
    
    # ----- 3.3 结构化输出查询 -----
    print("\n--- 3.3 结构化输出查询 ---")
    
    # 定义输出结构
    class AIApplications(BaseModel):
        """AI应用领域结构化输出模型"""
        应用领域: str
        主要技术: List[str]
        关键优势: List[str]
        挑战: List[str]
    
    # 结构化输出查询
    structured_response = db.query(
        query="分析人工智能在医疗诊断中的应用",
        schema=AIApplications,  # 指定输出结构
        temperature=0.3  # 低温度有助于生成更结构化的输出
    )
    
    print("结构化输出查询结果:")
    if isinstance(structured_response.completion, dict):
        # 将字典转换为格式化输出
        print(f"应用领域: {structured_response.completion.get('应用领域', 'N/A')}")
        
        print("主要技术:")
        for tech in structured_response.completion.get('主要技术', []):
            print(f"  - {tech}")
            
        print("关键优势:")
        for advantage in structured_response.completion.get('关键优势', []):
            print(f"  - {advantage}")
            
        print("挑战:")
        for challenge in structured_response.completion.get('挑战', []):
            print(f"  - {challenge}")
    else:
        # 如果无法生成结构化输出，则显示原始文本
        print(structured_response.completion)

# =====================================================================
# 4. 文档管理（列表、获取、更新、删除）
# =====================================================================

def manage_documents(db: Morphik, doc_ids: List[str]):
    """
    演示文档管理功能，包括列表、获取、更新和删除
    """
    print("\n===== 4. 文档管理 =====")
    
    # ----- 4.1 列出文档 -----
    print("\n--- 4.1 列出文档 ---")
    
    # 列出所有文档
    all_docs = db.list_documents()
    print(f"数据库中共有 {len(all_docs)} 个文档")
    
    # 使用过滤器列出文档
    filtered_docs = db.list_documents(filters={"领域": "医疗"})
    print(f"医疗领域的文档数量: {len(filtered_docs)}")
    
    # ----- 4.2 获取文档 -----
    print("\n--- 4.2 获取文档 ---")
    
    # 通过 ID 获取文档
    if doc_ids:
        doc = db.get_document(doc_ids[0])
        print(f"获取到文档: {doc.filename}")
        print(f"  元数据: {doc.metadata}")
    
    # 通过文件名获取文档
    doc_by_name = db.get_document_by_filename("ai_in_healthcare.md")
    if doc_by_name:
        print(f"通过文件名获取到文档: {doc_by_name.external_id}")
    else:
        print("未找到指定文件名的文档")
    
    # ----- 4.3 更新文档 -----
    print("\n--- 4.3 更新文档 ---")
    
    # 更新文档元数据
    if doc_ids:
        updated_doc = db.update_document_metadata(
            doc_ids[0],
            metadata={"更新时间": "2024-05-02", "状态": "已审核"}
        )
        print(f"已更新文档元数据: {updated_doc.external_id}")
        print(f"  新元数据: {updated_doc.metadata}")
    
    # 更新文档内容
    if doc_ids:
        print("更新文档内容示例 (注释掉，避免修改示例文档)")
        """
        updated_content_doc = db.update_document_with_text(
            doc_ids[0],
            content="这是更新后的文档内容。原始内容已被替换。",
            # 可选: 更新元数据
            metadata={"更新时间": "2024-05-02", "状态": "已修改"}
        )
        print(f"已更新文档内容: {updated_content_doc.external_id}")
        """
    
    # ----- 4.4 删除文档 -----
    print("\n--- 4.4 删除文档 ---")
    
    print("删除文档示例 (注释掉，避免删除示例文档)")
    """
    # 通过 ID 删除文档
    if doc_ids:
        db.delete_document(doc_ids[0])
        print(f"已删除文档: {doc_ids[0]}")
    
    # 通过文件名删除文档
    db.delete_document_by_filename("simple_doc.txt")
    print("已删除文件名为 simple_doc.txt 的文档")
    """

# =====================================================================
# 主函数
# =====================================================================

def main():
    """主函数：按顺序演示 Morphik 的基础功能"""
    print("Morphik Python SDK 教程 - 第一部分：基础功能")
    print("=" * 60)
    
    try:
        # 1. 初始化 Morphik 客户端
        db = initialize_morphik()
        
        # 2. 文档摄取
        doc_ids = ingest_documents(db)
        
        # 3. 基础检索与查询
        retrieve_and_query(db, doc_ids)
        
        # 4. 文档管理
        manage_documents(db, doc_ids)
        
        print("\n教程第一部分完成！")
        print("下一部分将介绍更高级的功能，如知识图谱、多模态搜索、缓存增强生成等。")
        
    except Exception as e:
        print(f"教程执行过程中出错: {e}")
    finally:
        # 确保关闭连接
        if 'db' in locals():
            db.close()
            print("已关闭 Morphik 连接")

if __name__ == "__main__":
    main()
