services:

  morphik:
    build: .
    ports:
      - "8000:8000"
    environment:
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-your-secret-key-here}
      - POSTGRES_URI=postgresql+asyncpg://morphik:morphik@postgres:5432/morphik
      - PGPASSWORD=morphik
      - HOST=0.0.0.0
      - PORT=8000
      - LOG_LEVEL=DEBUG
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - DASHSCOPE_API_KEY=${DASHSCOPE_API_KEY}
    volumes:
      - ./storage:/app/storage
      - ./logs:/app/logs
      - ./morphik.toml:/app/morphik.toml
      - huggingface_cache:/root/.cache/huggingface
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - morphik-network
    env_file:
      - .env

  worker:
    build: .
    command: arq core.workers.ingestion_worker.WorkerSettings
    environment:
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-your-secret-key-here}
      - POSTGRES_URI=postgresql+asyncpg://morphik:morphik@postgres:5432/morphik
      - PGPASSWORD=morphik
      - LOG_LEVEL=DEBUG
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - DASHSCOPE_API_KEY=${DASHSCOPE_API_KEY}
    volumes:
      - ./storage:/app/storage
      - ./logs:/app/logs
      - ./morphik.toml:/app/morphik.toml
      - huggingface_cache:/root/.cache/huggingface
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - morphik-network
    env_file:
      - .env

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5
    networks:
      - morphik-network

  postgres:
    image: pgvector/pgvector:pg15
    shm_size: 128mb
    environment:
      - POSTGRES_USER=morphik
      - POSTGRES_PASSWORD=morphik
      - POSTGRES_DB=morphik
      - PGDATA=/var/lib/postgresql/data/pgdata
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U morphik -d morphik"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    networks:
      - morphik-network



networks:
  morphik-network:
    driver: bridge

volumes:
  postgres_data:
  huggingface_cache:
  redis_data:
