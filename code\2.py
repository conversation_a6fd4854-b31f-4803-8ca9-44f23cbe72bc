#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Morphik Python SDK 完整教程 - 第二部分：高级功能
==============================================

本教程将循序渐进地介绍 Morphik Python SDK 的主要功能，包含详细的中文注释。
教程分为多个部分，从基础功能开始，逐步深入到高级特性。

第二部分内容：
1. 知识图谱创建与查询
2. 多模态搜索（ColPali）
3. 用户和文件夹管理
4. 缓存增强生成（Cache-Augmented-Generation）

作者：AI助手
日期：2024年
"""

import os
import time
from typing import Dict, List, Any, Optional, Type, Union
from pydantic import BaseModel, Field

# 导入 Morphik 相关模块
from morphik import Morphik
from morphik.models import (
    EntityExtractionPromptOverride, 
    EntityExtractionExample,
    EntityResolutionPromptOverride,
    EntityResolutionExample,
    GraphPromptOverrides,
    QueryPromptOverride,
    QueryPromptOverrides
)

# =====================================================================
# 1. 知识图谱创建与查询
# =====================================================================

def knowledge_graph_demo(db: Morphik):
    """
    演示知识图谱的创建和使用
    
    知识图谱是 Morphik 的强大功能，可以从文档中提取实体和关系，
    构建结构化的知识网络，用于更智能的查询和推理。
    """
    print("\n===== 1. 知识图谱创建与查询 =====")
    
    # ----- 1.1 准备示例文档 -----
    print("\n--- 1.1 准备示例文档 ---")
    
    # 摄取一些示例文档用于创建知识图谱
    tech_doc1 = db.ingest_text(
        content="""
        人工智能（AI）是计算机科学的一个分支，致力于创建能够模拟人类智能的系统。
        机器学习是AI的一个子领域，专注于开发能从数据中学习的算法。
        深度学习是机器学习的一种特殊形式，使用神经网络进行学习。
        
        谷歌的AlphaGo是一个著名的AI系统，它在2016年击败了围棋世界冠军李世石。
        AlphaGo由DeepMind开发，DeepMind是谷歌在2014年收购的AI研究公司。
        """,
        filename="ai_overview.txt",
        metadata={"领域": "技术", "主题": "人工智能"}
    )
    
    tech_doc2 = db.ingest_text(
        content="""
        自然语言处理（NLP）是AI的一个重要分支，专注于计算机理解和生成人类语言。
        GPT（生成式预训练转换器）是由OpenAI开发的一系列大型语言模型。
        GPT-4是该系列的最新版本，展示了前所未有的语言理解和生成能力。
        
        OpenAI由Sam Altman领导，是人工智能研究领域的领先组织之一。
        微软与OpenAI建立了战略合作伙伴关系，并投资了数十亿美元。
        """,
        filename="nlp_models.txt",
        metadata={"领域": "技术", "主题": "自然语言处理"}
    )
    
    print(f"已摄取示例文档用于知识图谱创建: {tech_doc1.external_id}, {tech_doc2.external_id}")
    
    # 等待文档索引完成
    print("等待文档索引完成...")
    time.sleep(3)
    
    # ----- 1.2 创建基本知识图谱 -----
    print("\n--- 1.2 创建基本知识图谱 ---")
    
    # 创建基本知识图谱
    graph = db.create_graph(
        name="ai_tech_graph",  # 图谱名称
        filters={"领域": "技术"}  # 基于元数据过滤选择文档
    )
    
    print(f"已创建知识图谱: {graph.name}")
    print("知识图谱创建是异步进行的，需要等待处理完成")
    
    # 等待图谱处理完成
    print("等待图谱处理完成...")
    graph = db.wait_for_graph_completion("ai_tech_graph")
    
    # 显示图谱信息
    print(f"图谱处理完成，包含 {len(graph.entities)} 个实体和 {len(graph.relationships)} 个关系")
    
    # 显示部分实体
    print("\n实体示例:")
    for i, entity in enumerate(graph.entities[:5]):  # 只显示前5个
        print(f"  实体 {i+1}: {entity.label} (类型: {entity.type})")
    
    # 显示部分关系
    print("\n关系示例:")
    for i, rel in enumerate(graph.relationships[:5]):  # 只显示前5个
        print(f"  关系 {i+1}: {rel.source} -> {rel.type} -> {rel.target}")
    
    # ----- 1.3 创建自定义知识图谱 -----
    print("\n--- 1.3 创建自定义知识图谱 ---")
    
    # 使用自定义提示词和实体示例创建图谱
    custom_graph = db.create_graph(
        name="custom_ai_graph",
        filters={"领域": "技术"},
        prompt_overrides=GraphPromptOverrides(
            # 自定义实体提取
            entity_extraction=EntityExtractionPromptOverride(
                # 可选：自定义提示词模板
                prompt_template="从以下文本中提取AI相关实体:\n\n{content}\n\n关注这些类型的实体:\n{examples}\n\n以JSON格式返回。",
                # 提供实体示例，指导模型提取特定类型的实体
                examples=[
                    EntityExtractionExample(label="人工智能", type="技术领域"),
                    EntityExtractionExample(label="GPT-4", type="模型"),
                    EntityExtractionExample(label="OpenAI", type="公司"),
                    EntityExtractionExample(label="Sam Altman", type="人物")
                ]
            ),
            # 自定义实体解析（合并相似实体）
            entity_resolution=EntityResolutionPromptOverride(
                examples=[
                    EntityResolutionExample(
                        canonical="人工智能",  # 规范名称
                        variants=["AI", "人工智能", "Artificial Intelligence"]  # 变体
                    ),
                    EntityResolutionExample(
                        canonical="OpenAI",
                        variants=["OpenAI公司", "OpenAI组织"]
                    )
                ]
            )
        )
    )
    
    print(f"已创建自定义知识图谱: {custom_graph.name}")
    print("等待自定义图谱处理完成...")
    custom_graph = db.wait_for_graph_completion("custom_ai_graph")
    
    print(f"自定义图谱处理完成，包含 {len(custom_graph.entities)} 个实体和 {len(custom_graph.relationships)} 个关系")
    
    # ----- 1.4 使用知识图谱进行查询 -----
    print("\n--- 1.4 使用知识图谱进行查询 ---")
    
    # 基于知识图谱的查询
    graph_response = db.query(
        query="解释机器学习与深度学习的关系，以及它们在AI领域的地位",
        graph_name="ai_tech_graph",  # 指定使用的图谱
        hop_depth=2,  # 考虑最多2跳的关系
        include_paths=True,  # 在结果中包含路径信息
        temperature=0.7
    )
    
    print("基于知识图谱的查询结果:")
    print(graph_response.completion)
    
    # 显示图谱路径（如果包含）
    if graph_response.metadata and "graph" in graph_response.metadata:
        print("\n图谱路径:")
        for path in graph_response.metadata["graph"]["paths"]:
            print("  " + " -> ".join(path))
    
    # 使用自定义提示词的图谱查询
    custom_graph_response = db.query(
        query="描述OpenAI开发的主要模型及其特点",
        graph_name="custom_ai_graph",
        hop_depth=2,
        prompt_overrides=QueryPromptOverrides(
            query=QueryPromptOverride(
                prompt_template="作为AI专家，请基于知识图谱详细回答以下问题，包括技术细节和历史背景：{question}"
            )
        )
    )
    
    print("\n使用自定义提示词的图谱查询结果:")
    print(custom_graph_response.completion)

# =====================================================================
# 2. 多模态搜索（ColPali）
# =====================================================================

def multimodal_search_demo(db: Morphik):
    """
    演示多模态搜索功能（ColPali）
    
    ColPali 是 Morphik 的多模态搜索功能，能够理解和搜索文档中的视觉内容。
    注意：此示例需要实际的图像文件才能完整运行。
    """
    print("\n===== 2. 多模态搜索（ColPali）=====")
    
    # ----- 2.1 多模态文档摄取 -----
    print("\n--- 2.1 多模态文档摄取 ---")
    print("注意: 此示例需要实际图像文件才能完整运行")
    
    # 创建包含图像描述的文本文档
    image_description_doc = db.ingest_text(
        content="""
        这是一张销售数据图表的描述。图表显示了2023年各季度的销售额：
        - 第一季度：100万元
        - 第二季度：150万元
        - 第三季度：200万元
        - 第四季度：250万元
        
        图表清晰地显示了销售额的持续增长趋势，第四季度达到了全年最高点。
        """,
        filename="sales_chart_description.txt",
        metadata={"类型": "图表描述", "主题": "销售数据"},
        # 启用 ColPali 多模态处理
        use_colpali=True
    )
    
    print(f"已摄取图表描述文档: {image_description_doc.external_id}")
    
    # 摄取图像文件的示例代码（注释掉，因为需要实际文件）
    """
    # 摄取包含图表的PDF文件
    chart_doc = db.ingest_file(
        file="path/to/sales_chart.pdf",
        metadata={"类型": "图表", "主题": "销售数据"},
        use_colpali=True  # 启用 ColPali 多模态处理
    )
    print(f"已摄取图表文件: {chart_doc.external_id}")
    
    # 摄取图像文件
    image_doc = db.ingest_file(
        file="path/to/product_image.jpg",
        metadata={"类型": "产品图片", "产品": "智能手机"},
        use_colpali=True
    )
    print(f"已摄取图像文件: {image_doc.external_id}")
    """
    
    # 等待文档索引完成
    print("等待文档索引完成...")
    time.sleep(3)
    
    # ----- 2.2 多模态检索 -----
    print("\n--- 2.2 多模态检索 ---")
    
    # 使用 ColPali 检索包含图像的块
    multimodal_chunks = db.retrieve_chunks(
        query="2023年第四季度的销售额是多少？",
        use_colpali=True,
        k=3
    )
    
    print(f"多模态检索到 {len(multimodal_chunks)} 个相关块")
    for i, chunk in enumerate(multimodal_chunks):
        print(f"  块 {i+1} - 相关度: {chunk.score:.4f}")
        # 如果是图像块，会返回 PIL.Image 对象
        if hasattr(chunk, 'is_image') and chunk.is_image:
            print("  这是一个图像块")
        else:
            # 显示文本内容的前100个字符
            print(f"  内容预览: {chunk.content[:100]}...")
    
    # ----- 2.3 多模态查询 -----
    print("\n--- 2.3 多模态查询 ---")
    
    # 使用 ColPali 进行多模态查询
    multimodal_response = db.query(
        query="分析2023年销售数据的趋势并总结主要发现",
        use_colpali=True,
        k=3
    )
    
    print("多模态查询结果:")
    print(multimodal_response.completion)

# =====================================================================
# 3. 用户和文件夹管理
# =====================================================================

def user_folder_management_demo(db: Morphik):
    """
    演示用户和文件夹管理功能
    
    Morphik 支持通过用户和文件夹组织和隔离数据，
    这为构建多租户应用和跨项目组织文档提供了便利。
    """
    print("\n===== 3. 用户和文件夹管理 =====")
    
    # ----- 3.1 文件夹管理 -----
    print("\n--- 3.1 文件夹管理 ---")
    
    # 创建文件夹
    research_folder = db.create_folder(
        name="research_projects",
        description="研究项目相关文档"
    )
    print(f"已创建文件夹: {research_folder.name}")
    
    # 在文件夹中摄取文档
    folder_doc = research_folder.ingest_text(
        content="这是一份研究项目的概述文档，包含项目目标、方法和预期成果。",
        filename="project_overview.txt",
        metadata={"项目": "AI研究", "状态": "进行中"}
    )
    print(f"已在文件夹中摄取文档: {folder_doc.external_id}")
    
    # 在文件夹中查询
    folder_response = research_folder.query(
        query="总结项目的主要目标",
        filters={"状态": "进行中"}
    )
    
    print("文件夹查询结果:")
    print(folder_response.completion)
    
    # ----- 3.2 用户管理 -----
    print("\n--- 3.2 用户管理 ---")
    
    # 创建用户作用域
    user_scope = db.signin("user123")
    print(f"已创建用户作用域: user123")
    
    # 在用户作用域中摄取文档
    user_doc = user_scope.ingest_text(
        content="这是用户专属的笔记，只对该用户可见。",
        filename="private_notes.txt",
        metadata={"类型": "私人笔记", "重要性": "高"}
    )
    print(f"已在用户作用域中摄取文档: {user_doc.external_id}")
    
    # 在用户作用域中查询
    user_response = user_scope.query(
        query="我的笔记内容是什么？",
        filters={"类型": "私人笔记"}
    )
    
    print("用户作用域查询结果:")
    print(user_response.completion)
    
    # ----- 3.3 组合用户和文件夹 -----
    print("\n--- 3.3 组合用户和文件夹 ---")
    
    # 创建用户特定的文件夹作用域
    user_folder_scope = research_folder.signin("user123")
    print("已创建用户特定的文件夹作用域: user123 in research_projects")
    
    # 在组合作用域中摄取文档
    combined_doc = user_folder_scope.ingest_text(
        content="这是用户在特定项目中的工作笔记，仅对该用户在该项目中可见。",
        filename="project_notes.txt",
        metadata={"类型": "工作笔记", "项目": "AI研究"}
    )
    print(f"已在组合作用域中摄取文档: {combined_doc.external_id}")
    
    # 在组合作用域中查询
    combined_response = user_folder_scope.query(
        query="我在AI研究项目中的笔记内容是什么？"
    )
    
    print("组合作用域查询结果:")
    print(combined_response.completion)

# =====================================================================
# 4. 缓存增强生成（Cache-Augmented-Generation）
# =====================================================================

def cache_augmented_generation_demo(db: Morphik):
    """
    演示缓存增强生成（CAG）功能
    
    缓存增强生成通过创建文档的持久化 KV 缓存来加速生成过程，
    减少计算成本并提高响应速度。
    """
    print("\n===== 4. 缓存增强生成 =====")
    
    # ----- 4.1 准备示例文档 -----
    print("\n--- 4.1 准备示例文档 ---")
    
    # 摄取一些示例文档用于缓存
    cache_doc = db.ingest_text(
        content="""
        Python是一种高级编程语言，以其简洁、易读的语法而闻名。
        Python支持多种编程范式，包括面向对象、命令式和函数式编程。
        Python的主要特点包括：
        1. 简洁易读的语法
        2. 动态类型系统
        3. 自动内存管理
        4. 丰富的标准库
        5. 跨平台兼容性
        
        Python广泛应用于Web开发、数据分析、人工智能、科学计算和自动化等领域。
        流行的Python库和框架包括NumPy、Pandas、TensorFlow、PyTorch、Django和Flask。
        """,
        filename="python_overview.txt",
        metadata={"领域": "编程", "语言": "Python"}
    )
    
    print(f"已摄取示例文档用于缓存: {cache_doc.external_id}")
    
    # 等待文档索引完成
    print("等待文档索引完成...")
    time.sleep(3)
    
    # ----- 4.2 创建缓存 -----
    print("\n--- 4.2 创建缓存 ---")
    
    # 创建缓存
    # 注意：这需要在服务器上配置好相应的模型
    try:
        cache_result = db.create_cache(
            name="python_cache",  # 缓存名称
            model="llama2",  # 模型名称
            gguf_file="llama-2-7b-chat.Q4_K_M.gguf",  # GGUF文件
            filters={"领域": "编程", "语言": "Python"}  # 文档过滤条件
        )
        
        print(f"已创建缓存: {cache_result}")
        
        # 获取缓存引用
        cache = db.get_cache("python_cache")
        
        # 更新缓存以处理文档
        print("更新缓存以处理文档...")
        cache.update()
        print("缓存更新完成")
        
        # ----- 4.3 查询缓存 -----
        print("\n--- 4.3 查询缓存 ---")
        
        # 直接查询缓存
        cache_response = cache.query(
            query="Python的主要特点是什么？",
            max_tokens=200,  # 生成的最大令牌数
            temperature=0.7  # 生成的随机性
        )
        
        print("缓存查询结果:")
        print(cache_response.completion)
        
        # ----- 4.4 添加文档到缓存 -----
        print("\n--- 4.4 添加文档到缓存 ---")
        
        # 摄取新文档
        new_doc = db.ingest_text(
            content="""
            Python的性能优化技巧：
            1. 使用适当的数据结构
            2. 利用生成器减少内存使用
            3. 使用内置函数和库
            4. 考虑使用PyPy等替代解释器
            5. 关键部分使用Cython或C扩展
            """,
            filename="python_optimization.txt",
            metadata={"领域": "编程", "语言": "Python", "主题": "性能优化"}
        )
        
        print(f"已摄取新文档: {new_doc.external_id}")
        
        # 将新文档添加到缓存
        cache.add_docs([new_doc.external_id])
        
        # 更新缓存以处理新文档
        print("更新缓存以处理新文档...")
        cache.update()
        print("缓存更新完成")
        
        # 查询更新后的缓存
        updated_response = cache.query(
            query="如何优化Python代码的性能？"
        )
        
        print("更新后的缓存查询结果:")
        print(updated_response.completion)
        
    except Exception as e:
        print(f"缓存操作失败: {e}")
        print("注意: 缓存功能需要在服务器上配置好相应的模型")

# =====================================================================
# 主函数
# =====================================================================

def main():
    """主函数：按顺序演示 Morphik 的高级功能"""
    print("Morphik Python SDK 教程 - 第二部分：高级功能")
    print("=" * 60)
    
    try:
        # 初始化 Morphik 客户端
        db = Morphik(is_local=True)
        
        # 1. 知识图谱创建与查询
        knowledge_graph_demo(db)
        
        # 2. 多模态搜索（ColPali）
        multimodal_search_demo(db)
        
        # 3. 用户和文件夹管理
        user_folder_management_demo(db)
        
        # 4. 缓存增强生成
        cache_augmented_generation_demo(db)
        
        print("\n教程第二部分完成！")
        print("下一部分将介绍更多高级功能和实际应用场景。")
        
    except Exception as e:
        print(f"教程执行过程中出错: {e}")
    finally:
        # 确保关闭连接
        if 'db' in locals():
            db.close()
            print("已关闭 Morphik 连接")

if __name__ == "__main__":
    main()
