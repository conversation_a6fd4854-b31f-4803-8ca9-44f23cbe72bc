#!/bin/bash

# Morphik 服务管理脚本
# 用于启动、停止、重启和查看 Morphik 服务状态

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查 Docker 是否运行
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        print_message $RED "❌ Docker 未运行，请先启动 Docker"
        exit 1
    fi
}

# 显示服务状态
show_status() {
    print_message $BLUE "📊 服务状态检查"
    echo "=================================="

    # 检查 Docker 容器状态
    print_message $YELLOW "🐳 Docker 容器状态:"
    docker compose ps 2>/dev/null || echo "没有运行的容器"

    echo ""

    # 检查端口占用
    print_message $YELLOW "🔌 端口占用情况:"
    echo "PostgreSQL (5433): $(lsof -i :5433 >/dev/null 2>&1 && echo '✅ 占用' || echo '❌ 空闲')"
    echo "Redis (6379):      $(lsof -i :6379 >/dev/null 2>&1 && echo '✅ 占用' || echo '❌ 空闲')"
    echo "Morphik API (8000): $(lsof -i :8000 >/dev/null 2>&1 && echo '✅ 占用' || echo '❌ 空闲')"
    echo "Ollama (11434):    $(lsof -i :11434 >/dev/null 2>&1 && echo '✅ 占用' || echo '❌ 空闲')"

    echo ""

    # 检查 API 健康状态
    print_message $YELLOW "🏥 API 健康检查:"

    # 检查完整的 Morphik API
    if curl -s http://localhost:8000/ping >/dev/null 2>&1; then
        echo "✅ 完整的 Morphik API 正常运行"

        # 检查具体功能端点
        echo "📋 功能端点检查:"
        if curl -s http://localhost:8000/ingest/text -X POST -H "Content-Type: application/json" -d '{}' >/dev/null 2>&1; then
            echo "  ✅ 文档摄取端点可用"
        else
            echo "  ❌ 文档摄取端点不可用"
        fi

        if curl -s http://localhost:8000/query -X POST -H "Content-Type: application/json" -d '{}' >/dev/null 2>&1; then
            echo "  ✅ 查询端点可用"
        else
            echo "  ❌ 查询端点不可用"
        fi

    elif curl -s http://localhost:8000/health >/dev/null 2>&1; then
        echo "⚠️  简化版 Morphik API 正在运行（功能有限）"
    else
        echo "❌ Morphik API 未响应"
    fi

    # 检查 Ollama 服务
    if curl -s http://localhost:11434/api/tags >/dev/null 2>&1; then
        echo "✅ Ollama 服务正常运行"

        # 检查已安装的模型
        models=$(curl -s http://localhost:11434/api/tags | python3 -c "import json, sys; data=json.load(sys.stdin); print(', '.join([m['name'] for m in data.get('models', [])]))" 2>/dev/null)
        if [ ! -z "$models" ]; then
            echo "  📦 已安装模型: $models"
        fi
    else
        echo "❌ Ollama 服务未响应"
    fi
}

# 停止所有服务
stop_services() {
    print_message $YELLOW "🛑 停止所有服务..."

    # 停止完整的 Morphik API 进程
    print_message $BLUE "停止 Morphik API 进程..."
    if [ -f ".morphik_pid" ]; then
        MORPHIK_PID=$(cat .morphik_pid | grep "Morphik API PID:" | cut -d: -f2 | tr -d ' ')
        if [ ! -z "$MORPHIK_PID" ]; then
            print_message $BLUE "停止 Morphik API (PID: $MORPHIK_PID)..."
            kill $MORPHIK_PID 2>/dev/null || echo "进程可能已经停止"
            sleep 2
            # 如果进程仍在运行，强制杀死
            if kill -0 $MORPHIK_PID 2>/dev/null; then
                kill -9 $MORPHIK_PID 2>/dev/null || echo "强制停止进程"
            fi
        fi
        rm -f .morphik_pid
    fi

    # 停止其他可能运行的 Python 进程 - 更强力的停止方法
    print_message $BLUE "停止其他 Python API 进程..."

    # 停止所有 Morphik 相关进程
    pkill -9 -f "start_full_morphik" 2>/dev/null || echo "没有运行的完整 Morphik 进程"
    pkill -9 -f "simple_morphik_app" 2>/dev/null || echo "没有运行的简化 Morphik 进程"
    pkill -9 -f "local_start" 2>/dev/null || echo "没有运行的本地启动进程"

    # 停止所有 uvicorn 和 API 进程
    pkill -9 -f "uvicorn.*morphik" 2>/dev/null || echo "没有运行的 Uvicorn 进程"
    pkill -9 -f "uvicorn.*core.api" 2>/dev/null || echo "没有运行的核心 API 进程"
    pkill -9 -f "core.api:app" 2>/dev/null || echo "没有运行的核心 API 进程"
    pkill -9 -f "python.*core.api" 2>/dev/null || echo "没有运行的 Python API 进程"

    # 停止端口 8000 上的所有进程
    lsof -ti:8000 | xargs kill -9 2>/dev/null || echo "端口 8000 上没有运行的进程"

    # 停止 ARQ worker 进程
    print_message $BLUE "停止 ARQ worker 进程..."
    pkill -9 -f "arq.*worker" 2>/dev/null || echo "没有运行的 ARQ worker 进程"
    pkill -9 -f "ingestion_worker" 2>/dev/null || echo "没有运行的摄取 worker 进程"

    # 停止 Docker 容器
    print_message $BLUE "停止 Docker 容器..."
    docker compose down 2>/dev/null || echo "没有运行的容器需要停止"

    # 等待端口释放
    sleep 5

    print_message $GREEN "✅ 所有服务已停止"
}

# 检查 morphik.toml 中的 ColPali 配置
check_colpali_config() {
    if [ -f "morphik.toml" ]; then
        # 使用 grep 和 awk 来解析 TOML 文件中的 enable_colpali 配置，去除注释
        COLPALI_ENABLED=$(grep -E "^\s*enable_colpali\s*=" morphik.toml | awk -F'=' '{print $2}' | awk -F'#' '{print $1}' | tr -d ' ' | tr -d '"' | tr -d "'")
        if [ "$COLPALI_ENABLED" = "true" ]; then
            return 0  # ColPali 启用
        else
            return 1  # ColPali 禁用
        fi
    else
        print_message $YELLOW "⚠️  未找到 morphik.toml 配置文件，默认禁用 ColPali"
        return 1  # 默认禁用
    fi
}

# 启动基础服务（PostgreSQL, Redis）
start_base_services() {
    print_message $YELLOW "🚀 启动基础服务..."

    check_docker

    # 启动基础服务
    print_message $BLUE "启动 PostgreSQL, Redis..."
    docker compose up -d postgres redis

    # 等待服务启动
    print_message $BLUE "等待服务启动..."
    sleep 10

    # 检查 Docker 服务状态
    if docker compose ps | grep -q "Up"; then
        print_message $GREEN "✅ Docker 基础服务启动成功"
    else
        print_message $RED "❌ Docker 基础服务启动失败"
        exit 1
    fi

    # 检查本机 Ollama 服务
    print_message $BLUE "检查本机 Ollama 服务..."
    if curl -s http://localhost:11434/api/tags >/dev/null 2>&1; then
        print_message $GREEN "✅ 本机 Ollama 服务正常运行"
    else
        print_message $YELLOW "⚠️  本机 Ollama 服务未运行，请手动启动:"
        print_message $BLUE "   运行命令: ollama serve"
        print_message $BLUE "   或在新终端中运行: ollama serve &"
    fi
}

# 统一的 Morphik API 启动函数（根据配置决定是否启用 ColPali）
start_morphik_api() {
    # 检查 ColPali 配置
    if check_colpali_config; then
        print_message $YELLOW "🚀 启动完整的 Morphik API (包含 ColPali 多模态功能)..."
        COLPALI_STATUS="启用"
        WAIT_TIME=60
        COLPALI_ENV=""
    else
        print_message $YELLOW "🚀 启动完整的 Morphik API (ColPali 已禁用)..."
        COLPALI_STATUS="禁用"
        WAIT_TIME=30
        COLPALI_ENV="DISABLE_COLPALI=1"
    fi

    # 检查基础服务是否运行
    if ! docker compose ps | grep -q "Up"; then
        print_message $YELLOW "基础服务未运行，先启动基础服务..."
        start_base_services
    fi

    # 启动完整的 Morphik API
    print_message $BLUE "启动完整的 Morphik API 服务 (ColPali: $COLPALI_STATUS)..."
    if [ -f "start_full_morphik.py" ]; then
        # 确保 logs 目录存在
        mkdir -p logs

        # 根据配置启动服务
        if [ "$COLPALI_STATUS" = "启用" ]; then
            python3 start_full_morphik.py --host 0.0.0.0 --port 8000 > logs/morphik_api.log 2>&1 &
            print_message $BLUE "等待 Morphik API 启动 (包括 ColPali 模型加载)..."
            print_message $BLUE "这可能需要 60-120 秒，请耐心等待..."
            print_message $YELLOW "⚠️  ColPali 模型较大，首次启动会下载模型文件"
        else
            DISABLE_COLPALI=1 python3 start_full_morphik.py --host 0.0.0.0 --port 8000 > logs/morphik_api.log 2>&1 &
            print_message $BLUE "等待 Morphik API 启动（包括模型加载）..."
            print_message $BLUE "这可能需要 30-60 秒，请耐心等待..."
        fi

        MORPHIK_PID=$!
        sleep $WAIT_TIME

        # 检查服务是否启动成功
        if curl -s http://localhost:8000/ping >/dev/null 2>&1; then
            if [ "$COLPALI_STATUS" = "启用" ]; then
                print_message $GREEN "✅ 完整的 Morphik API (含 ColPali) 启动成功"
            else
                print_message $GREEN "✅ 完整的 Morphik API 启动成功"
            fi
            print_message $BLUE "🌐 访问地址:"
            echo "   - API: http://localhost:8000"
            echo "   - 文档: http://localhost:8000/docs"
            echo "   - 健康检查: http://localhost:8000/ping"
            echo "   - 文档摄取: http://localhost:8000/ingest/text"
            echo "   - 文档查询: http://localhost:8000/query"
            if [ "$COLPALI_STATUS" = "启用" ]; then
                echo "   - 多模态摄取: http://localhost:8000/ingest/file (支持图像)"
            fi
            print_message $BLUE "📋 日志文件: logs/morphik_api.log"
            echo "Morphik API PID: $MORPHIK_PID" > .morphik_pid
        else
            print_message $RED "❌ Morphik API 启动失败"
            print_message $YELLOW "请检查日志文件: logs/morphik_api.log"
            if [ "$COLPALI_STATUS" = "启用" ]; then
                print_message $YELLOW "ColPali 模型可能仍在加载中，请等待更长时间"
            fi
        fi
    else
        print_message $RED "❌ 找不到 start_full_morphik.py 文件"
        print_message $YELLOW "回退到简化版 API..."

        # 回退到简化版本
        if [ -f "simple_morphik_app.py" ]; then
            python3 simple_morphik_app.py &
            sleep 3

            if curl -s http://localhost:8000/health >/dev/null 2>&1; then
                print_message $GREEN "✅ 简化版 Morphik API 启动成功"
                print_message $BLUE "🌐 访问地址:"
                echo "   - API: http://localhost:8000"
                echo "   - 文档: http://localhost:8000/docs"
                echo "   - 健康检查: http://localhost:8000/health"
            else
                print_message $RED "❌ API 启动失败"
            fi
        fi
    fi
}

# 启动所有服务（根据配置决定是否启用 ColPali）
start_all_services() {
    print_message $YELLOW "🚀 启动所有服务..."
    start_base_services
    start_morphik_api
}

# 重启所有服务
restart_services() {
    print_message $YELLOW "🔄 重启所有服务..."
    stop_services
    sleep 3
    start_all_services
}

# 显示日志
show_logs() {
    print_message $BLUE "📋 显示服务日志"
    echo "=================================="

    print_message $YELLOW "Docker 容器日志:"
    docker compose logs --tail=50 2>/dev/null || echo "没有容器日志"
}

# 显示帮助信息
show_help() {
    echo "Morphik 服务管理脚本"
    echo "===================="
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start           启动所有服务 (基础服务 + Morphik API)"
    echo "  stop            停止所有服务 (包括 Docker 容器和 Python 进程)"
    echo "  restart         重启所有服务"
    echo "  status          显示详细的服务状态和功能检查"
    echo "  logs            显示服务日志"
    echo "  help            显示此帮助信息"
    echo ""
    echo "ColPali 多模态功能:"
    echo "  ColPali 功能的启用/禁用由 morphik.toml 配置文件中的 enable_colpali 设置决定"
    echo "  - enable_colpali = true   启用 ColPali 多模态功能 (支持图像处理)"
    echo "  - enable_colpali = false  禁用 ColPali 功能 (仅支持文本)"
    echo ""
    echo "示例:"
    echo "  $0 start       # 启动所有服务 (根据配置决定是否启用 ColPali)"
    echo "  $0 stop        # 停止所有服务"
    echo "  $0 status      # 查看服务状态"
    echo "  $0 restart     # 重启所有服务"
}

# 主逻辑
case "${1:-help}" in
    "start")
        start_all_services
        ;;
    "stop")
        stop_services
        ;;
    "restart")
        restart_services
        ;;
    "status")
        show_status
        ;;
    "logs")
        show_logs
        ;;
    # 保留旧命令以兼容性（但不在帮助中显示）
    "start-base")
        start_base_services
        ;;
    "start-api")
        start_morphik_api
        ;;
    "start-all")
        start_all_services
        ;;
    "help"|*)
        show_help
        ;;
esac
