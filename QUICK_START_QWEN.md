# Morphik + 阿里云通义千问 快速开始指南

## 🚀 5分钟快速部署

### 前提条件
- ✅ Docker 和 Docker Compose 已安装
- ✅ 阿里云账号和 DashScope API 密钥

### 第一步：获取 API 密钥
1. 访问 [阿里云 DashScope 控制台](https://dashscope.console.aliyun.com/)
2. 登录并创建 API 密钥
3. 复制您的 API 密钥（格式：sk-xxxxxxxxxxxxxx）

### 第二步：配置环境
```bash
# 编辑 .env 文件
nano .env

# 设置您的 API 密钥
DASHSCOPE_API_KEY=sk-your-actual-api-key-here
```

### 第三步：测试 API 连接（可选）
```bash
# 安装依赖
pip install requests python-dotenv

# 测试 API 连接
python test_qwen_api.py
```

### 第四步：一键部署
```bash
# 运行部署脚本
./deploy_morphik_qwen.sh
```

### 第五步：验证部署
```bash
# 验证部署状态
./verify_deployment.sh

# 或手动检查
curl http://localhost:8000/health
```

## 🎯 快速使用

### API 测试
```bash
# 健康检查
curl http://localhost:8000/health

# 查看 API 文档
open http://localhost:8000/docs
```

### Python SDK 示例
```python
from morphik import Morphik

# 连接到本地 Morphik 实例
morphik = Morphik(uri="http://localhost:8000")

# 上传文档
doc = morphik.ingest_file(file_path="document.pdf")
doc.wait_for_completion()

# 查询文档
response = morphik.query(query="文档的主要内容是什么？")
print(response)
```

## 🔧 常用命令

```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs morphik

# 重启服务
docker-compose restart morphik

# 停止服务
docker-compose down

# 完全清理（删除所有数据）
docker-compose down -v
```

## 📊 服务地址

- **Morphik API**: http://localhost:8000
- **API 文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health

## ❓ 遇到问题？

1. **查看详细日志**: `docker-compose logs morphik`
2. **检查 API 密钥**: 确保 `.env` 文件中的密钥正确
3. **验证网络**: 确保能访问阿里云 API
4. **查看文档**: 阅读 `MORPHIK_QWEN_DEPLOYMENT.md`

## 🎉 部署成功！

现在您可以开始使用 Morphik 进行：
- 📄 文档处理和分析
- 🔍 智能问答
- 🕸️ 知识图谱构建
- 🤖 AI 代理工作流
