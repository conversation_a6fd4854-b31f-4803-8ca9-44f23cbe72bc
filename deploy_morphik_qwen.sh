#!/bin/bash

# Morphik 阿里云通义千问部署脚本
# 此脚本将部署 Morphik 系统，使用阿里云通义千问作为 LLM 提供者

set -e

echo "🚀 开始部署 Morphik 系统（使用阿里云通义千问）"
echo "=================================================="

# 检查 Docker 和 Docker Compose 是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ 错误: Docker 未安装。请先安装 Docker。"
    exit 1
fi

if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo "❌ 错误: Docker Compose 未安装。请先安装 Docker Compose。"
    exit 1
fi

# 检查 .env 文件是否存在
if [ ! -f ".env" ]; then
    echo "❌ 错误: .env 文件不存在。请确保 .env 文件已创建并配置了 DASHSCOPE_API_KEY。"
    exit 1
fi

# 检查是否配置了阿里云 API 密钥
if ! grep -q "DASHSCOPE_API_KEY=" .env || grep -q "DASHSCOPE_API_KEY=your-dashscope-api-key-here" .env; then
    echo "⚠️  警告: 请在 .env 文件中配置您的阿里云 DashScope API 密钥"
    echo "   编辑 .env 文件，将 DASHSCOPE_API_KEY 设置为您的实际 API 密钥"
    echo "   获取 API 密钥: https://dashscope.console.aliyun.com/"
    read -p "   配置完成后按 Enter 继续..."
fi

echo "📋 部署配置检查"
echo "- ✅ Docker 已安装"
echo "- ✅ Docker Compose 已安装"
echo "- ✅ .env 文件存在"
echo "- ✅ 配置文件已更新为使用阿里云通义千问"

echo ""
echo "🔧 开始构建和启动服务..."

# 停止现有服务（如果有）
echo "停止现有服务..."
docker-compose down 2>/dev/null || true

# 构建并启动服务
echo "构建 Docker 镜像..."
docker-compose build

echo "启动 PostgreSQL 和 Redis..."
docker-compose up -d postgres redis

# 等待数据库启动
echo "等待数据库启动..."
sleep 10

# 检查数据库健康状态
echo "检查数据库连接..."
for i in {1..30}; do
    if docker-compose exec -T postgres pg_isready -U morphik -d morphik >/dev/null 2>&1; then
        echo "✅ 数据库连接成功"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ 数据库启动超时"
        exit 1
    fi
    echo "等待数据库启动... ($i/30)"
    sleep 2
done

# 启动 Morphik 主服务和 Worker
echo "启动 Morphik 服务..."
docker-compose up -d morphik worker

echo ""
echo "🎉 部署完成！"
echo "=================================================="
echo "服务访问地址:"
echo "- Morphik API: http://localhost:8000"
echo "- API 文档: http://localhost:8000/docs"
echo "- 健康检查: http://localhost:8000/health"
echo ""
echo "数据库连接:"
echo "- PostgreSQL: localhost:5433"
echo "- Redis: localhost:6379"
echo ""
echo "📊 查看服务状态:"
echo "docker-compose ps"
echo ""
echo "📝 查看日志:"
echo "docker-compose logs morphik"
echo "docker-compose logs worker"
echo ""
echo "🛑 停止服务:"
echo "docker-compose down"
echo ""
echo "⚠️  重要提醒:"
echo "1. 请确保在 .env 文件中配置了正确的 DASHSCOPE_API_KEY"
echo "2. 首次启动可能需要几分钟来初始化数据库"
echo "3. 如遇问题，请查看日志文件进行故障排除"
