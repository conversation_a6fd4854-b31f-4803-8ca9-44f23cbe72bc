#!/usr/bin/env python3
"""
阿里云通义千问 API 连接测试脚本
此脚本用于验证阿里云 DashScope API 密钥是否正确配置
"""

import os
import sys
import requests
import json
from dotenv import load_dotenv

def test_qwen_api():
    """测试阿里云通义千问 API 连接"""
    
    print("🔍 测试阿里云通义千问 API 连接")
    print("=" * 40)
    
    # 加载环境变量
    load_dotenv()
    
    # 获取 API 密钥
    api_key = os.getenv('DASHSCOPE_API_KEY')
    
    if not api_key:
        print("❌ 错误: 未找到 DASHSCOPE_API_KEY 环境变量")
        print("请在 .env 文件中设置您的阿里云 API 密钥")
        return False
    
    if api_key == "your-dashscope-api-key-here":
        print("❌ 错误: API 密钥未正确配置")
        print("请将 .env 文件中的 DASHSCOPE_API_KEY 设置为您的实际 API 密钥")
        return False
    
    print(f"✅ 找到 API 密钥: {api_key[:10]}...")
    
    # 测试对话模型
    print("\n🤖 测试对话模型 (qwen-max-latest)...")
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    chat_data = {
        "model": "qwen-max-latest",
        "messages": [
            {
                "role": "user",
                "content": "你好，请简单介绍一下你自己。"
            }
        ],
        "max_tokens": 100,
        "temperature": 0.7
    }
    
    try:
        response = requests.post(
            'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions',
            headers=headers,
            json=chat_data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content']
                print(f"✅ 对话模型测试成功")
                print(f"   响应: {content[:100]}...")
            else:
                print("❌ 对话模型响应格式异常")
                print(f"   响应: {result}")
                return False
        else:
            print(f"❌ 对话模型测试失败 (状态码: {response.status_code})")
            print(f"   错误信息: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 对话模型请求失败: {e}")
        return False
    
    # 测试嵌入模型
    print("\n📊 测试嵌入模型 (text-embedding-v3)...")
    
    embedding_data = {
        "model": "text-embedding-v3",
        "input": "这是一个测试文本",
        "encoding_format": "float"
    }
    
    try:
        response = requests.post(
            'https://dashscope.aliyuncs.com/compatible-mode/v1/embeddings',
            headers=headers,
            json=embedding_data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if 'data' in result and len(result['data']) > 0:
                embedding = result['data'][0]['embedding']
                print(f"✅ 嵌入模型测试成功")
                print(f"   嵌入维度: {len(embedding)}")
                print(f"   前5个值: {embedding[:5]}")
            else:
                print("❌ 嵌入模型响应格式异常")
                print(f"   响应: {result}")
                return False
        else:
            print(f"❌ 嵌入模型测试失败 (状态码: {response.status_code})")
            print(f"   错误信息: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 嵌入模型请求失败: {e}")
        return False
    
    print("\n🎉 所有 API 测试通过！")
    print("阿里云通义千问 API 配置正确，可以正常使用。")
    return True

def main():
    """主函数"""
    success = test_qwen_api()
    
    if success:
        print("\n✅ API 测试完成，您可以继续部署 Morphik 系统")
        print("运行部署脚本: ./deploy_morphik_qwen.sh")
        sys.exit(0)
    else:
        print("\n❌ API 测试失败，请检查配置后重试")
        print("\n🔧 故障排除建议:")
        print("1. 确保 .env 文件存在且包含正确的 DASHSCOPE_API_KEY")
        print("2. 检查 API 密钥是否有效（访问 https://dashscope.console.aliyun.com/）")
        print("3. 确保网络连接正常")
        print("4. 检查 API 密钥是否有足够的配额")
        sys.exit(1)

if __name__ == "__main__":
    main()
