# Morphik 阿里云通义千问部署指南

## 📋 概述

本指南将帮助您使用 Docker Compose 部署 Morphik 系统，配置阿里云通义千问作为大语言模型提供者，替代本地 Ollama 服务。

## 🎯 部署目标

- ✅ 使用 Docker Compose 部署 Morphik 核心服务
- ✅ 配置 PostgreSQL 数据库（带 pgvector 扩展）
- ✅ 配置 Redis 缓存服务
- ✅ 集成阿里云通义千问 API
- ✅ 移除对 Ollama 的依赖

## 🔧 系统要求

### 硬件要求
- **内存**: 至少 4GB RAM（推荐 8GB+）
- **存储**: 至少 10GB 可用磁盘空间
- **CPU**: 多核处理器（推荐 2 核+）

### 软件要求
- Docker 和 Docker Compose
- 阿里云 DashScope API 密钥

## 📦 部署组件

### 1. 核心服务
- **Morphik API**: 主要的 API 服务器
- **Morphik Worker**: 后台处理服务
- **PostgreSQL**: 带 pgvector 扩展的数据库
- **Redis**: 缓存和消息队列

### 2. 模型配置
- **主要 LLM**: qwen-max-latest（阿里云通义千问最新版本）
- **嵌入模型**: text-embedding-v3（阿里云通义千问嵌入模型）
- **重排序器**: BAAI/bge-reranker-large（本地运行）

## 🚀 部署步骤

### 第一步：获取阿里云 API 密钥

1. 访问 [阿里云 DashScope 控制台](https://dashscope.console.aliyun.com/)
2. 登录您的阿里云账号
3. 创建 API 密钥
4. 记录您的 API 密钥（格式类似：sk-xxxxxxxxxxxxxx）

### 第二步：配置环境变量

编辑 `.env` 文件，设置您的 API 密钥：

```bash
# 将 your-dashscope-api-key-here 替换为您的实际 API 密钥
DASHSCOPE_API_KEY=sk-your-actual-api-key-here
```

### 第三步：执行部署

#### 方法一：使用部署脚本（推荐）

```bash
# 运行自动部署脚本
./deploy_morphik_qwen.sh
```

#### 方法二：手动部署

```bash
# 1. 停止现有服务
docker-compose down

# 2. 构建镜像
docker-compose build

# 3. 启动数据库服务
docker-compose up -d postgres redis

# 4. 等待数据库启动（约 30 秒）
sleep 30

# 5. 启动 Morphik 服务
docker-compose up -d morphik worker
```

### 第四步：验证部署

```bash
# 检查服务状态
docker-compose ps

# 查看服务日志
docker-compose logs morphik

# 测试 API 连接
curl http://localhost:8000/health
```

## 🔍 服务访问

- **Morphik API**: http://localhost:8000
- **API 文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health
- **PostgreSQL**: localhost:5433
- **Redis**: localhost:6379

## 📁 数据存储

- **数据库数据**: Docker volume `postgres_data`
- **文档存储**: `./storage` 目录
- **日志文件**: `./logs` 目录
- **缓存数据**: Docker volume `redis_data`

## 🎮 使用示例

### 基本 API 测试

```bash
# 健康检查
curl http://localhost:8000/health

# 查看 API 文档
open http://localhost:8000/docs
```

### Python SDK 使用

```python
from morphik import Morphik

# 初始化客户端
morphik = Morphik(uri="http://localhost:8000")

# 上传文档
doc = morphik.ingest_file(file_path="your_document.pdf")
doc.wait_for_completion()

# 查询文档
response = morphik.query(query="文档的主要内容是什么？")
print(response)
```

## 🔄 服务管理

### 启动所有服务
```bash
docker-compose up -d
```

### 停止所有服务
```bash
docker-compose down
```

### 重启特定服务
```bash
docker-compose restart morphik
docker-compose restart worker
```

### 查看日志
```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs morphik
docker-compose logs worker
docker-compose logs postgres
```

### 清理和重置
```bash
# 停止并删除所有容器和卷（会删除所有数据）
docker-compose down -v

# 删除所有镜像
docker-compose down --rmi all
```

## 🔧 故障排除

### 常见问题

#### 1. API 密钥配置错误
```bash
# 检查环境变量
docker-compose exec morphik env | grep DASHSCOPE

# 确保 .env 文件中的 API 密钥正确
cat .env | grep DASHSCOPE_API_KEY
```

#### 2. 服务启动失败
```bash
# 查看详细错误日志
docker-compose logs morphik
docker-compose logs worker

# 检查端口占用
netstat -tulpn | grep :8000
netstat -tulpn | grep :5433
netstat -tulpn | grep :6379
```

#### 3. 数据库连接问题
```bash
# 测试数据库连接
docker-compose exec postgres psql -U morphik -d morphik -c "SELECT version();"

# 检查数据库健康状态
docker-compose exec postgres pg_isready -U morphik -d morphik
```

#### 4. 内存不足
- 确保系统有足够的可用内存（至少 4GB）
- 考虑关闭其他占用内存的应用程序
- 调整 Docker 内存限制

### 性能优化

#### 1. 调整重排序器设备
编辑 `morphik.toml` 中的 reranker 配置：
```toml
[reranker]
device = "cpu"  # 在 Docker 中使用 CPU
```

#### 2. 调整数据库连接池
```toml
[database]
pool_size = 5           # 减少连接池大小
max_overflow = 10       # 减少最大溢出连接
```

## 🌐 模型配置说明

### 已配置的阿里云模型

#### 对话模型
- `qwen_max_latest`: 通义千问最新版本（最强性能）
- `qwen_plus`: 通义千问增强版（平衡性能和成本）
- `qwen_turbo`: 通义千问快速版（高速响应）

#### 嵌入模型
- `qwen_embedding_v3`: 文本嵌入模型 v3（1024 维）
- `qwen_embedding_v2`: 文本嵌入模型 v2（兼容性）

### 切换模型

编辑 `morphik.toml` 文件中的相应配置：

```toml
[completion]
model = "qwen_plus"  # 切换到 qwen-plus 模型

[embedding]
model = "qwen_embedding_v2"  # 切换到 v2 嵌入模型
```

## 📊 监控和日志

### 查看系统资源使用
```bash
# 查看容器资源使用情况
docker stats

# 查看磁盘使用情况
docker system df
```

### 日志管理
```bash
# 实时查看日志
docker-compose logs -f morphik

# 查看最近的日志
docker-compose logs --tail=100 morphik
```

## 🔐 安全建议

### 生产环境部署
1. **更改默认密钥**:
   ```bash
   # 生成新的 JWT 密钥
   openssl rand -hex 32
   ```

2. **保护 API 密钥**:
   - 不要将 API 密钥提交到版本控制
   - 使用环境变量管理敏感信息

3. **网络安全**:
   - 限制端口访问
   - 使用防火墙规则
   - 配置 HTTPS

4. **定期备份**:
   ```bash
   # 备份数据库
   docker-compose exec postgres pg_dump -U morphik morphik > backup.sql
   
   # 备份存储目录
   tar -czf storage_backup.tar.gz storage/
   ```

## 📞 获取帮助

如果遇到问题，请：

1. **查看日志**: `docker-compose logs`
2. **检查官方文档**: https://www.morphik.ai/docs/
3. **GitHub Issues**: https://github.com/morphik-org/morphik-core/issues
4. **阿里云文档**: https://help.aliyun.com/zh/dashscope/

---

**部署完成！** 🎉 您现在可以开始使用 Morphik 进行文档处理和知识图谱构建了。
