#!/bin/bash

# Morphik 部署验证脚本
# 此脚本将验证 Morphik 系统是否正确部署和运行

set -e

echo "🔍 开始验证 Morphik 部署状态"
echo "================================"

# 检查服务状态
echo "📊 检查 Docker 服务状态..."
if ! docker-compose ps | grep -q "Up"; then
    echo "❌ 错误: 没有运行中的服务"
    echo "请先运行部署脚本: ./deploy_morphik_qwen.sh"
    exit 1
fi

echo "✅ Docker 服务正在运行"

# 检查各个服务
services=("postgres" "redis" "morphik" "worker")
for service in "${services[@]}"; do
    if docker-compose ps | grep "$service" | grep -q "Up"; then
        echo "✅ $service 服务运行正常"
    else
        echo "❌ $service 服务未运行"
        echo "查看日志: docker-compose logs $service"
    fi
done

echo ""
echo "🌐 检查网络连接..."

# 检查端口是否可访问
ports=("8000" "5433" "6379")
port_names=("Morphik API" "PostgreSQL" "Redis")

for i in "${!ports[@]}"; do
    port="${ports[$i]}"
    name="${port_names[$i]}"
    
    if nc -z localhost "$port" 2>/dev/null; then
        echo "✅ $name (端口 $port) 可访问"
    else
        echo "❌ $name (端口 $port) 不可访问"
    fi
done

echo ""
echo "🏥 检查服务健康状态..."

# 检查 Morphik API 健康状态
echo "检查 Morphik API 健康状态..."
if curl -s http://localhost:8000/health >/dev/null 2>&1; then
    echo "✅ Morphik API 健康检查通过"
    
    # 获取健康检查详细信息
    health_response=$(curl -s http://localhost:8000/health)
    echo "   响应: $health_response"
else
    echo "❌ Morphik API 健康检查失败"
    echo "   请检查服务日志: docker-compose logs morphik"
fi

# 检查数据库连接
echo ""
echo "检查数据库连接..."
if docker-compose exec -T postgres pg_isready -U morphik -d morphik >/dev/null 2>&1; then
    echo "✅ PostgreSQL 数据库连接正常"
    
    # 检查 pgvector 扩展
    if docker-compose exec -T postgres psql -U morphik -d morphik -c "SELECT * FROM pg_extension WHERE extname='vector';" | grep -q "vector"; then
        echo "✅ pgvector 扩展已安装"
    else
        echo "⚠️  pgvector 扩展未找到"
    fi
else
    echo "❌ PostgreSQL 数据库连接失败"
fi

# 检查 Redis 连接
echo ""
echo "检查 Redis 连接..."
if docker-compose exec -T redis redis-cli ping | grep -q "PONG"; then
    echo "✅ Redis 连接正常"
else
    echo "❌ Redis 连接失败"
fi

echo ""
echo "🔑 检查环境配置..."

# 检查环境变量
if docker-compose exec -T morphik env | grep -q "DASHSCOPE_API_KEY"; then
    api_key=$(docker-compose exec -T morphik env | grep "DASHSCOPE_API_KEY" | cut -d'=' -f2 | tr -d '\r')
    if [ "$api_key" != "your-dashscope-api-key-here" ] && [ -n "$api_key" ]; then
        echo "✅ 阿里云 API 密钥已配置"
    else
        echo "⚠️  阿里云 API 密钥未正确配置"
        echo "   请在 .env 文件中设置正确的 DASHSCOPE_API_KEY"
    fi
else
    echo "❌ 阿里云 API 密钥环境变量未找到"
fi

echo ""
echo "📋 系统信息摘要"
echo "================================"

# 显示服务状态摘要
echo "Docker 服务状态:"
docker-compose ps

echo ""
echo "系统资源使用:"
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"

echo ""
echo "🔗 访问地址"
echo "================================"
echo "- Morphik API: http://localhost:8000"
echo "- API 文档: http://localhost:8000/docs"
echo "- 健康检查: http://localhost:8000/health"

echo ""
echo "📝 常用命令"
echo "================================"
echo "查看日志: docker-compose logs [service_name]"
echo "重启服务: docker-compose restart [service_name]"
echo "停止服务: docker-compose down"

echo ""
echo "✅ 验证完成！"

# 提供下一步建议
echo ""
echo "🎯 下一步操作建议:"
echo "1. 访问 http://localhost:8000/docs 查看 API 文档"
echo "2. 使用 Python SDK 或 REST API 开始上传和查询文档"
echo "3. 查看示例代码: examples/ 目录"

if curl -s http://localhost:8000/health >/dev/null 2>&1; then
    echo ""
    echo "🎉 恭喜！Morphik 系统部署成功并正常运行！"
else
    echo ""
    echo "⚠️  部分服务可能存在问题，请查看上述检查结果并进行故障排除。"
fi
